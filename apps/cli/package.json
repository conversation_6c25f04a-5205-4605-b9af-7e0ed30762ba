{"name": "ever-works-cli", "version": "0.0.1", "description": "", "author": "", "private": true, "license": "UNLICENSED", "scripts": {"start": "nest start", "ever-works": "node dist/main", "build:cli": "node build.js", "build": "nest build", "build:deps": "pnpm --filter '@packages/*' build"}, "dependencies": {"@nestjs/common": "^11.0.1", "@nestjs/core": "^11.0.1", "@packages/agent": "workspace:*", "@types/fs-extra": "^11.0.4", "@types/inquirer": "^9.0.8", "chalk": "^4.1.2", "fs-extra": "^11.3.0", "inquirer": "^12.7.0", "nest-commander": "^3.17.0", "ora": "^5.4.1", "reflect-metadata": "^0.2.2", "rxjs": "^7.8.1"}, "devDependencies": {"@nestjs/cli": "^11.0.7", "@nestjs/schematics": "^11.0.5", "@swc/cli": "^0.6.0", "@swc/core": "^1.12.9", "@types/node": "^22.16.0", "esbuild": "^0.25.6", "globals": "^16.3.0", "source-map-support": "^0.5.21", "ts-loader": "^9.5.2", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "typescript": "^5.8.3"}}