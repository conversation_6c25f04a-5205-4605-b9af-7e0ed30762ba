const esbuild = require('esbuild');

esbuild
    .build({
        entryPoints: ['src/main.ts'],
        bundle: true,
        platform: 'node',
        target: 'node16',
        outfile: 'build/cli.js',
        banner: {
            js: '#!/usr/bin/env node',
        },
        external: ['@nestjs/microservices', '@nestjs/websockets/socket-module', 'better-sqlite3'],
    })
    .catch(() => process.exit(1));
